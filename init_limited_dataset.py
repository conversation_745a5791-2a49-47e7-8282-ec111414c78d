"""
Custom implementation of init_dataset that properly respects record limits.

This module provides a function that works exactly like braintrust.init_dataset()
but properly limits the number of records fetched, avoiding the pagination issue
in the original implementation.
"""

import braintrust
from typing import Optional, Dict, Any, Union, List, Iterator, Callable, cast
from braintrust.logger import (
    BraintrustState, 
    _state, 
    ObjectFetcher, 
    DatasetEvent,
    ProjectDatasetMetadata,
    ObjectMetadata,
    DEFAULT_IS_LEGACY_DATASET,
    ensure_dataset_record,
    _enrich_attachments,
    INTERNAL_BTQL_LIMIT,
    MAX_BTQL_ITERATIONS
)
from braintrust.util import LazyValue, response_raise_for_status


class LimitedObjectFetcher(ObjectFetcher[DatasetEvent]):
    """
    A custom ObjectFetcher that respects user-specified limits.
    """

    def __init__(
        self,
        object_type: str,
        pinned_version: Union[None, int, str] = None,
        mutate_record: Optional[Callable[[DatasetEvent], DatasetEvent]] = None,
        _internal_btql: Optional[Dict[str, Any]] = None,
        max_records: Optional[int] = None,
    ):
        super().__init__(object_type, pinned_version, mutate_record, _internal_btql)
        self.max_records = max_records
        self.last_cursor: Optional[str] = None
        self.has_more_data: bool = False
        
    def _refetch(self) -> List[DatasetEvent]:
        """
        Custom refetch that respects the max_records limit and tracks cursor.
        """
        state = self._get_state()
        if self._fetched_data is None:
            # Start with cursor from _internal_btql if provided
            cursor = self._internal_btql.get("cursor") if self._internal_btql else None
            data = None
            iterations = 0
            records_fetched = 0

            while True:
                # Calculate how many records we still need
                remaining_records = None
                if self.max_records is not None:
                    remaining_records = self.max_records - records_fetched
                    if remaining_records <= 0:
                        break

                # Use the smaller of INTERNAL_BTQL_LIMIT or remaining_records
                current_limit = INTERNAL_BTQL_LIMIT
                if remaining_records is not None:
                    current_limit = min(INTERNAL_BTQL_LIMIT, remaining_records)

                # Build query, excluding cursor from _internal_btql to avoid conflicts
                btql_params = {k: v for k, v in (self._internal_btql or {}).items() if k != "cursor"}

                resp = state.api_conn().post(
                    f"btql",
                    json={
                        "query": {
                            **btql_params,
                            "select": [{"op": "star"}],
                            "from": {
                                "op": "function",
                                "name": {
                                    "op": "ident",
                                    "name": [self.object_type],
                                },
                                "args": [
                                    {
                                        "op": "literal",
                                        "value": self.id,
                                    },
                                ],
                            },
                            "cursor": cursor,
                            "limit": current_limit,
                        },
                        "use_columnstore": False,
                        "brainstore_realtime": True,
                    },
                    headers={
                        "Accept-Encoding": "gzip",
                    },
                )
                response_raise_for_status(resp)
                resp_json = resp.json()
                batch_data = cast(List[DatasetEvent], resp_json["data"])

                data = (data or []) + batch_data
                records_fetched += len(batch_data)

                # Store the cursor for potential pagination
                next_cursor = resp_json.get("cursor", None)

                # Check if we should stop
                should_stop = False
                if not next_cursor:
                    # No more data available
                    self.has_more_data = False
                    should_stop = True
                elif self.max_records is not None and records_fetched >= self.max_records:
                    # We've reached our limit, but there might be more data
                    self.has_more_data = bool(next_cursor)
                    self.last_cursor = next_cursor
                    should_stop = True
                elif len(batch_data) < current_limit:
                    # We got fewer records than requested, probably no more data
                    self.has_more_data = False
                    should_stop = True

                if should_stop:
                    break

                cursor = next_cursor
                iterations += 1
                if iterations > MAX_BTQL_ITERATIONS:
                    raise RuntimeError("Too many BTQL iterations")

            if not isinstance(data, list):
                raise ValueError(f"Expected a list in the response, got {type(data)}")

            # Apply record mutation if needed
            if self._mutate_record is not None:
                self._fetched_data = [self._mutate_record(r) for r in data]
            else:
                self._fetched_data = data

        return self._fetched_data


class LimitedDataset(LimitedObjectFetcher):
    """
    A dataset that properly respects record limits.
    """
    
    def __init__(
        self,
        lazy_metadata: LazyValue[ProjectDatasetMetadata],
        version: Union[None, int, str] = None,
        legacy: bool = DEFAULT_IS_LEGACY_DATASET,
        _internal_btql: Optional[Dict[str, Any]] = None,
        state: Optional[BraintrustState] = None,
        max_records: Optional[int] = None,
    ):
        if legacy:
            print(
                f"""Records will be fetched from this dataset in the legacy format, with the "expected" field renamed to "output". Please update your code to use "expected", and use `braintrust.init_dataset()` with `use_output=False`, which will become the default in a future version of Braintrust."""
            )

        def mutate_record(r: DatasetEvent) -> DatasetEvent:
            _enrich_attachments(cast(Dict[str, Any], r))
            return ensure_dataset_record(r, legacy)

        self._lazy_metadata = lazy_metadata
        self.new_records = 0

        LimitedObjectFetcher.__init__(
            self,
            object_type="dataset",
            pinned_version=version,
            mutate_record=mutate_record,
            _internal_btql=_internal_btql,
            max_records=max_records,
        )

        self.state = state or _state

    @property
    def id(self) -> str:
        return self._lazy_metadata.get().dataset.id

    @property
    def name(self) -> str:
        return self._lazy_metadata.get().dataset.name

    @property
    def data(self):
        return self._lazy_metadata.get().dataset.full_info

    @property
    def project(self):
        return self._lazy_metadata.get().project

    def _get_state(self) -> BraintrustState:
        # Ensure the login state is populated by fetching the lazy_metadata.
        self._lazy_metadata.get()
        return self.state

    # Cursor and pagination properties
    @property
    def cursor(self) -> Optional[str]:
        """Get the cursor for the next page of results."""
        # Ensure data is fetched first
        self._refetch()
        return self.last_cursor

    @property
    def has_more(self) -> bool:
        """Check if there are more records available for pagination."""
        # Ensure data is fetched first
        self._refetch()
        return self.has_more_data

    # Capture all metadata attributes which aren't covered by existing methods.
    def __getattr__(self, name: str) -> Any:
        return self._lazy_metadata.get().dataset.full_info[name]


def init_limited_dataset(
    project: Optional[str] = None,
    name: Optional[str] = None,
    description: Optional[str] = None,
    version: Optional[Union[str, int]] = None,
    app_url: Optional[str] = None,
    api_key: Optional[str] = None,
    org_name: Optional[str] = None,
    project_id: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    use_output: bool = DEFAULT_IS_LEGACY_DATASET,
    _internal_btql: Optional[Dict[str, Any]] = None,
    state: Optional[BraintrustState] = None,
    max_records: Optional[int] = None,
    cursor: Optional[str] = None,
) -> LimitedDataset:
    """
    Create a new limited dataset that respects the max_records parameter.

    This function works exactly like braintrust.init_dataset() but properly
    limits the number of records fetched and provides cursor support for pagination.

    Args:
        max_records: Maximum number of records to fetch. If None, fetches all records.
        cursor: Optional cursor to start fetching from (for pagination).
        All other parameters are the same as braintrust.init_dataset()

    Returns:
        A LimitedDataset object that behaves like a regular Dataset but respects limits.
        Use dataset.cursor to get the cursor for the next page.
        Use dataset.has_more to check if more records are available.
    """
    
    def compute_metadata():
        login_state = state or _state
        login_state.login(
            org_name=org_name,
            api_key=api_key,
            app_url=app_url,
        )

        args = {
            "org_id": login_state.org_id,
            "project_name": project,
            "project_id": project_id,
            "dataset_name": name,
            "description": description,
            "metadata": metadata,
        }
        response = login_state.app_conn().post_json("api/dataset/register", args)

        return ProjectDatasetMetadata(
            project=ObjectMetadata(
                id=response["project"]["id"],
                name=response["project"]["name"],
                full_info=response["project"],
            ),
            dataset=ObjectMetadata(
                id=response["dataset"]["id"],
                name=response["dataset"]["name"],
                full_info=response["dataset"],
            ),
        )

    # Add cursor to _internal_btql if provided
    btql_params = _internal_btql or {}
    if cursor is not None:
        btql_params = {**btql_params, "cursor": cursor}

    return LimitedDataset(
        lazy_metadata=LazyValue(compute_metadata, use_mutex=True),
        version=version,
        legacy=use_output,
        _internal_btql=btql_params,
        state=state,
        max_records=max_records,
    )


# Example usage:
if __name__ == "__main__":
    # This will only fetch 3 records
    # dataset = init_limited_dataset(
    #     project="pedro-project1",
    #     name="themes",
    #     max_records=3
    # )

    # records = []
    # for record in dataset:
    #     records.append(record)

    # print(f"Fetched {len(records)} records")
    # print(f"Has more data: {dataset.has_more}")
    # print(f"Cursor for next page: {dataset.cursor}")

    # # If there's more data, fetch the next page
    # if dataset.has_more and dataset.cursor:
    #     print("\nFetching next page...")
    #     next_dataset = init_limited_dataset(
    #         project="pedro-project1",
    #         name="themes",
    #         max_records=3,
    #         cursor=dataset.cursor
    #     )

    #     next_records = list(next_dataset)
    #     print(f"Fetched {len(next_records)} more records")
    #     print(f"Has more data: {next_dataset.has_more}")
    #     print(f"Next cursor: {next_dataset.cursor}")


    dataset_has_more = True
    project_name="pedro-project1",
    dataset_name="themes",
    records_to_fetch=3
    cursor = None
    while dataset_has_more:
        if cursor == None:
            dataset = init_limited_dataset(project=project_name, name=dataset_name, max_records=records_to_fetch)
            cursor = dataset.cursor
        else:
            dataset = init_limited_dataset(project=project_name, name=dataset_name, max_records=records_to_fetch, cursor=cursor)
            cursor = dataset.cursor
        for i in dataset:
            print(i['input'])